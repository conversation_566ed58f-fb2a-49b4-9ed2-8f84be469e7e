import { Locator, Page } from '@playwright/test';
import { BasePage } from '@pages/basePage.page';

export class FormatsTabPage extends BasePage {
  private page: Page;
  readonly selectFormat = (text: string): Locator => this.page.getByRole('cell', { name: text, exact: true });
  readonly formatsTable: Locator;
  // Right side buttons
  readonly addFormatButton: Locator;
  readonly addApiButton: Locator;
  readonly uploadFormatButton: Locator;
  readonly manageMappingsButton: Locator;
  readonly assignCollectionButton: Locator;
  readonly assignOutputButton: Locator;
  readonly deleteButton: Locator;
  // delete dialog
  readonly confirmDeleteButton: Locator;
  readonly confirmDeleteCancelButton: Locator;
  // Add API dialog
  readonly tradingPartnerDropdown: Locator;
  readonly dialogSpinner: Locator;
  readonly successNotification: Locator;
  readonly errorNotification: Locator;
  readonly categoryDropdown: Locator;
  readonly selectTradingPartnerDropdownItem = (text: string): Locator => this.page.getByText(text, { exact: true });
  readonly selectCategoryDropdownItem = (text: string): Locator =>
    this.page.getByRole('option').getByText(text, { exact: true });
  readonly createButton: Locator;
  readonly cancelButton: Locator;
  // Create Format File dialog
  readonly companyNameInput: Locator;
  readonly categoryNameInput: Locator;
  readonly companyImageUrlInput: Locator;
  readonly formatFileUploader: Locator;

  constructor(page: Page) {
    super(page);
    this.page = page;
    this.formatsTable = this.page.locator('.q-table');
    this.dialogSpinner = this.page.locator('.c-inri-spinner');
    this.successNotification = this.page
      .locator('.q-notification__message')
      .filter({ hasText: 'format file created successfully' });
    this.errorNotification = this.page
      .locator('.q-notification__message')
      .filter({ hasText: 'error when creating format file' });
    this.addFormatButton = this.page.getByTestId('settings-menu');
    this.addApiButton = this.page.getByTestId('add-api');
    this.uploadFormatButton = this.page.getByTestId('upload-format-file');
    this.manageMappingsButton = this.page.getByLabel('manage mappings');
    this.assignCollectionButton = this.page.getByLabel('assign collections');
    this.assignOutputButton = this.page.getByLabel('assign outputs');
    this.deleteButton = this.page.getByLabel('delete');
    this.confirmDeleteButton = this.page.locator('.block').filter({ hasText: 'delete' });
    this.confirmDeleteCancelButton = this.page.locator('.block').filter({ hasText: 'cancel' });
    this.tradingPartnerDropdown = this.page.getByTestId('trading-partners-select');
    this.categoryDropdown = this.page.getByTestId('categories-select');
    this.createButton = this.page.getByRole('button', { name: 'Create' });
    this.cancelButton = this.page.getByRole('button', { name: 'Cancel' });
    this.companyNameInput = this.page.getByTestId('company-name-input');
    this.categoryNameInput = this.page.getByTestId('category-name-input');
    this.companyImageUrlInput = this.page.getByTestId('company-image-url-input');
    this.formatFileUploader = this.page.getByTestId('format-file-uploader');
  }

  async deleteFormat(name: string): Promise<void> {
    await this.selectFormat(name).click();
    await this.deleteButton.click();
    await this.confirmDeleteButton.click();
  }

  async uploadFormatFile(companyName: string, categoryName: string): Promise<void> {
    const filePath = './test/src/page-objects/welcomePage/welcomeSettings/formatsTab/FormatFile.xlsx';
    await this.companyNameInput.fill(companyName);
    await this.categoryNameInput.fill(categoryName);
    const fileInput = this.formatFileUploader.locator('input[type="file"]');
    await fileInput.setInputFiles(filePath);
    await this.createButton.click();
  }
}
