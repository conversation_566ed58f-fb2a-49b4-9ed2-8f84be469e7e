import { Schedule, ScheduleResponse } from '@core/interfaces';
import { portalFetch, proxyPortalFetch } from '@utils';
import { convertDateToLocalFormat } from '@services/helper/dateHelpers';
import { convertUtcCronToLocalCron } from '@helpers/DateTimeHelpers';
import cronstrue from 'cronstrue';

async function readSchedulesThroughProxy(url: string): Promise<Schedule[]> {
  const response = await proxyPortalFetch(url);
  if (response.status === 404) {
    console.error('Schedules not found');
    return [];
  }

  const schedules = response?.ok ? await response.json() : [];
  return toHistoryList(schedules);
}

async function readSchedulesWithoutProxy(url: string): Promise<Schedule[]> {
  const response = await portalFetch(url);
  if (response.status === 404) {
    console.error('Schedules not found');
    return [];
  }

  return toHistoryList(await response.json());
}

export const getSchedulesByTargetCompany = async (
  companyName: string,
  pageNumber: number,
  pageSize: number
): Promise<Schedule[]> => {
  const url = `/api/syndicate-advance/schedule/targetCompany/${companyName}?pageNumber=${pageNumber}&pageSize=${pageSize}`;

  try {
    if (import.meta.env.DEV) {
      return await readSchedulesThroughProxy(url);
    }
    return await readSchedulesWithoutProxy(url);
  } catch (error) {
    console.error('Error fetching schedules', error);
    return [];
  }
};

export const deleteSchedule = async (id: number): Promise<boolean> => {
  const url = `/api/syndicate-advance/schedule/${id}`;

  if (import.meta.env.DEV) {
    const response = await proxyPortalFetch(url, { method: 'DELETE' });
    return response.ok;
  }

  return (await portalFetch(url, { method: 'DELETE' })).ok;
};

const toHistoryList = (responseList: ScheduleResponse[]): Schedule[] => {
  return responseList.map((schedule) => {
    // Use the schedule's start date as reference for cron conversion to ensure consistency
    const localCron = schedule.CronExpression
      ? convertUtcCronToLocalCron(schedule.CronExpression, schedule.StartDate)
      : undefined;
    return {
      id: schedule.Id,
      name: schedule.Name,
      collectionName: schedule.CollectionName,
      mappingName: schedule.MappingName,
      outputName: schedule.OutputName,
      cronExpression: localCron
        ? cronstrue.toString(localCron)
        : `Once at ${convertDateToLocalFormat(schedule.StartDate)}`,
      startDate: convertDateToLocalFormat(schedule.StartDate),
      lastExecution: convertDateToLocalFormat(schedule.LastExecution),
      updatedBy: schedule.UpdatedBy,
    };
  });
};
