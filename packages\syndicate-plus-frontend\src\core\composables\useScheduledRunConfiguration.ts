import { computed, ref, watch } from 'vue';
import { Day, Frequency } from '@enums/ApiSyndication';
import { ScheduledRunConfiguration } from '@core/interfaces';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import {
  calculateCronExpression,
  convertLocalToUtcString,
  getNextExecutionDateTime,
  isDateTimeInFuture,
} from '@helpers/DateTimeHelpers';

dayjs.extend(utc);

export function useScheduledRunConfiguration() {
  // Constants
  const dateTimeMask = 'YYYY/MM/DD HH:mm';
  const timeMask = 'HH:mm';
  const inputMask = '####/##/## ##:##';
  const timeInputMask = '##:##';

  // Refs
  const scheduleName = ref<string>();
  const selectedFrequency = ref<Frequency>();
  const startDate = ref<string>(dayjs().format(dateTimeMask));
  const selectedDays = ref<Day[]>([]);

  // Flag to prevent setter from overriding watcher changes
  const watcherIsUpdating = ref<boolean>(false);

  // Track whether user has provided valid input
  const hasValidUserInput = ref<boolean>(false);

  // Computed for input value - always shows what the user should see
  const currentInputValue = computed({
    get: () => {
      const needsDateTime = selectedFrequency.value === Frequency.ONCE || selectedFrequency.value === Frequency.MONTHLY;
      return needsDateTime ? startDate.value : dayjs(startDate.value, dateTimeMask).format(timeMask);
    },
    set: (value: string) => {
      // Ignore setter calls immediately after watcher updates
      if (watcherIsUpdating.value) {
        return;
      }

      const needsDateTime = selectedFrequency.value === Frequency.ONCE || selectedFrequency.value === Frequency.MONTHLY;

      if (needsDateTime) {
        // For ONCE/MONTHLY: only accept complete date-time values
        if (value && value.match(/^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}$/)) {
          startDate.value = value;
          hasValidUserInput.value = true;
        } else {
          // User provided invalid or empty input
          hasValidUserInput.value = false;
        }
      } else {
        // For DAILY/WEEKLY: use today's date with the entered time
        if (value && value.match(/^\d{2}:\d{2}$/)) {
          const [hours, minutes] = value.split(':');
          const hoursNum = parseInt(hours);
          const minutesNum = parseInt(minutes);

          // Validate time ranges: hours 0-23, minutes 0-59
          if (hoursNum >= 0 && hoursNum <= 23 && minutesNum >= 0 && minutesNum <= 59) {
            const newDateTime = dayjs().hour(hoursNum).minute(minutesNum).format(dateTimeMask);
            startDate.value = newDateTime;
            hasValidUserInput.value = true;
          } else {
            // User provided invalid time values
            hasValidUserInput.value = false;
          }
        } else {
          // User provided invalid or empty input
          hasValidUserInput.value = false;
        }
      }
    },
  });

  // Computed properties
  const isStartDateVisible = computed(() => !!selectedFrequency.value);
  const isDaysVisible = computed(() => !!selectedFrequency.value && selectedFrequency.value === Frequency.WEEKLY);
  const requiresDateTime = computed(
    () => selectedFrequency.value === Frequency.ONCE || selectedFrequency.value === Frequency.MONTHLY
  );
  const isValidStartDate = computed(() => {
    // If startDate is empty, it's invalid regardless of user input flag
    if (!startDate.value) {
      return false;
    }

    // Only check if date is in future for ONCE frequency
    // For recurring schedules (DAILY, WEEKLY, MONTHLY), the cron scheduler
    // will automatically find the next valid execution time
    if (selectedFrequency.value === Frequency.ONCE) {
      const executionDateTime = buildExecutionDateTime();
      const isInFuture = isDateTimeInFuture(executionDateTime);
      return hasValidUserInput.value && isInFuture;
    }

    // For other frequencies (DAILY, WEEKLY, MONTHLY), check if user provided valid input
    return hasValidUserInput.value;
  });
  const isInvalid = computed(() => {
    if (!scheduleName.value) {
      return true;
    }

    switch (selectedFrequency.value) {
      case Frequency.ONCE: {
        const isInvalid = !isValidStartDate.value;
        return isInvalid;
      }

      case Frequency.DAILY:
      case Frequency.MONTHLY:
        const isInvalid = !isValidStartDate.value;
        return isInvalid;

      case Frequency.WEEKLY: {
        const isInvalid = !isValidStartDate.value || !selectedDays.value.length;
        return isInvalid;
      }
    }

    return true;
  });

  // Functions
  const buildExecutionDateTime = (): string => {
    // startDate always contains the full date-time we want to use
    return startDate.value || '';
  };

  const onDayClick = (day: Day) => {
    if (selectedDays.value.includes(day)) {
      selectedDays.value = selectedDays.value.filter((x) => x !== day);
    } else {
      selectedDays.value.push(day);
    }
  };

  const getConfiguration = (): ScheduledRunConfiguration | undefined => {
    if (!scheduleName.value || !selectedFrequency.value) {
      return undefined;
    }

    const executionDateTime = buildExecutionDateTime();
    const utcStartDate = convertLocalToUtcString(executionDateTime);
    const utcEndDate = null; // Always save as null per requirements
    const cronExpression = calculateCronExpression(selectedFrequency.value, selectedDays.value, utcStartDate);
    const nextExecution = getNextExecutionDateTime(cronExpression, selectedFrequency.value, utcStartDate);

    const configuration = {
      name: scheduleName.value,
      cronExpression: cronExpression,
      nextExecution: nextExecution,
      startDate: utcStartDate,
      endDate: utcEndDate,
    };

    return configuration;
  };

  // Watcher to handle frequency changes and maintain correct date/time values
  watch(selectedFrequency, (newFrequency, oldFrequency) => {
    if (!newFrequency) {
      hasValidUserInput.value = false;
      return;
    }

    // Set flag to prevent setter from overriding our changes
    watcherIsUpdating.value = true;

    // If this is the first selection, use current date/time
    if (!oldFrequency) {
      startDate.value = dayjs().format(dateTimeMask);
      hasValidUserInput.value = true; // Initial value from watcher is considered valid
      setTimeout(() => {
        watcherIsUpdating.value = false;
      }, 100);
      return;
    }

    // When switching frequencies, adjust the date appropriately
    const currentDateTime = dayjs(startDate.value, dateTimeMask);
    const needsDateTime = newFrequency === Frequency.ONCE || newFrequency === Frequency.MONTHLY;

    if (needsDateTime) {
      // For ONCE/MONTHLY: keep the current date and time if valid, otherwise use current date/time
      if (currentDateTime.isValid() && startDate.value.match(/^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}$/)) {
        startDate.value = currentDateTime.format(dateTimeMask);
        hasValidUserInput.value = true; // Keep valid state when preserving existing valid input
      } else {
        // If switching from DAILY/WEEKLY to ONCE/MONTHLY, preserve the time from the current input
        const currentInput = currentInputValue.value;
        if (currentInput && currentInput.match(/^\d{2}:\d{2}$/)) {
          // Use today's date with the user's selected time
          const [hours, minutes] = currentInput.split(':');
          startDate.value = dayjs().hour(parseInt(hours)).minute(parseInt(minutes)).format(dateTimeMask);
          hasValidUserInput.value = true; // User had valid time input, keep it valid
        } else {
          // Fallback to current date/time only if no valid time is available
          startDate.value = dayjs().format(dateTimeMask);
          hasValidUserInput.value = true; // Watcher-provided fallback is considered valid
        }
      }
    } else {
      // For DAILY/WEEKLY: always use today's date with current time
      startDate.value = dayjs().format(dateTimeMask);
      hasValidUserInput.value = true; // Watcher-provided value is considered valid
    }

    // Clear flag after a short delay to allow for any immediate reactivity
    setTimeout(() => {
      watcherIsUpdating.value = false;
    }, 100);
  });

  return {
    // Constants
    dateTimeMask,
    timeMask,
    inputMask,
    timeInputMask,

    // Reactive state
    scheduleName,
    selectedFrequency,
    selectedDays,
    currentInputValue,
    startDate, // Expose for testing

    // Computed properties
    isStartDateVisible,
    isDaysVisible,
    requiresDateTime,
    isValidStartDate,
    isInvalid,

    // Functions
    onDayClick,
    getConfiguration,
  };
}
