import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';
import { WelcomeSettingsPage } from '@pages/welcomePage/welcomeSettings/welcomeSettings.page';
import { FormatsTabPage } from '@pages/welcomePage/welcomeSettings/formatsTab/formatsTab.page';

test.describe('Formats tab', () => {
  const formatAccordions = 'Accordions';
  const tradingPartnerSafeName = 'best-buy';
  const tradingPartnerName = 'Best Buy';
  const category = 'Accordions';

  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;
  let welcomeSettingsPage: WelcomeSettingsPage;
  let formatTabPage: FormatsTabPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
    welcomeSettingsPage = new WelcomeSettingsPage(localPage);
    formatTabPage = new FormatsTabPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await welcomeToSyndicatePlusPage.settingsButton.click();
    await expect.soft(welcomeSettingsPage.formatsTab, 'FormatsTab is not visible').toBeVisible();
    await welcomeSettingsPage.formatsTab.click();
    await expect(formatTabPage.formatsTable, 'Formats Table is not visible').toBeVisible();
  });

  test('Add and delete format api', async () => {
    // Remove format if exists
    const existingFormat = await formatTabPage
      .selectFormat(`${tradingPartnerSafeName} - ${formatAccordions}`)
      .isVisible();
    if (existingFormat) {
      await formatTabPage.deleteFormat(`${tradingPartnerSafeName} - ${formatAccordions}`);
    }

    // Create new format
    await expect(async () => {
      await formatTabPage.addFormatButton.hover();
      await formatTabPage.addApiButton.click();
      await expect(formatTabPage.createButton).toBeEnabled();
      await expect(formatTabPage.dialogSpinner).toBeHidden();
    }).toPass();
    await formatTabPage.tradingPartnerDropdown.click();
    await formatTabPage.selectTradingPartnerDropdownItem(tradingPartnerName).click();
    await formatTabPage.categoryDropdown.click();
    await formatTabPage.selectCategoryDropdownItem(category).click();
    await formatTabPage.createButton.click();
    await formatTabPage.successNotification.waitFor({ state: 'visible' });
    await formatTabPage.successNotification.waitFor({ state: 'hidden' });
    await expect(
      formatTabPage.selectFormat(`${tradingPartnerSafeName} - ${formatAccordions}`),
      'Format is not visible'
    ).toBeVisible();

    // Delete format
    await formatTabPage.deleteFormat(`${tradingPartnerSafeName} - ${formatAccordions}`);
    await expect(formatTabPage.selectFormat(`${tradingPartnerSafeName} - ${formatAccordions}`)).not.toBeVisible();
  });

  test('Add and delete format file, verify table still shows other formats', async () => {
    const testCompanyName = 'Test Company';
    const testCategoryName = 'Test Category';
    const expectedFormatName = `${testCompanyName} - ${testCategoryName}`;
    const initialFormatCount = await formatTabPage.formatsTable.locator('tbody tr').count();

    const existingTestFormat = await formatTabPage.selectFormat(expectedFormatName).isVisible();
    if (existingTestFormat) {
      await formatTabPage.deleteFormat(expectedFormatName);
    }

    await expect(async () => {
      await formatTabPage.addFormatButton.hover();
      await formatTabPage.uploadFormatButton.click();
      await expect(formatTabPage.createButton).toBeDisabled();
      await expect(formatTabPage.dialogSpinner).toBeHidden();
    }).toPass();

    await formatTabPage.uploadFormatFile(testCompanyName, testCategoryName);
    await formatTabPage.successNotification.waitFor({ state: 'visible' });
    await formatTabPage.successNotification.waitFor({ state: 'hidden' });
    await expect(formatTabPage.selectFormat(expectedFormatName), 'Uploaded format is not visible').toBeVisible();
    const formatCountAfterUpload = await formatTabPage.formatsTable.locator('tbody tr').count();
    expect(formatCountAfterUpload).toBe(initialFormatCount + 1);

    await formatTabPage.deleteFormat(expectedFormatName);
    await expect(formatTabPage.selectFormat(expectedFormatName)).not.toBeVisible();
    const finalFormatCount = await formatTabPage.formatsTable.locator('tbody tr').count();
    expect(finalFormatCount).toBe(initialFormatCount);

    if (initialFormatCount > 0) {
      await expect(formatTabPage.formatsTable.locator('tbody tr')).toHaveCount(initialFormatCount);
      const firstFormat = formatTabPage.formatsTable.locator('tbody tr').first();
      await expect(firstFormat).toBeVisible();
    }
  });
});
