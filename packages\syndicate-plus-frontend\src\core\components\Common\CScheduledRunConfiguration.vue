<template>
  <div>
    <q-input
      v-model="scheduleName"
      v-bind="$inri.input"
      :label="$t('core.trading_partners.scheduled_syndication.schedule_name')"
      hide-bottom-space
    />
    <div class="frequency">
      <q-radio
        v-for="frequencyType in allFrequencyTypes"
        :key="frequencyType"
        v-model="selectedFrequency"
        :val="frequencyType"
        :label="frequencyType.toLocaleLowerCase()"
        class="pt-2"
      />
    </div>
    <q-input
      v-if="isStartDateVisible"
      v-model="currentInputValue"
      filled
      :mask="requiresDateTime ? inputMask : timeInputMask"
      :label="$t('core.trading_partners.scheduled_syndication.execution_time')"
      class="pt-2"
      data-testid="scheduled-run-configuration-date-time-input"
    >
      <template #prepend>
        <q-icon name="mdi-calendar-outline" class="cursor-pointer">
          <q-popup-proxy transition-show="scale" transition-hide="scale">
            <div class="q-pa-md">
              <div class="q-gutter-md row items-start">
                <q-date v-if="requiresDateTime" v-model="currentInputValue" :mask="dateTimeMask" />
                <q-time v-model="currentInputValue" :mask="requiresDateTime ? dateTimeMask : timeMask" now-btn />
              </div>
            </div>
          </q-popup-proxy>
        </q-icon>
      </template>
    </q-input>
    <q-btn-group v-if="isDaysVisible" class="days">
      <c-btn
        v-for="day in allDays"
        :key="day"
        :label="day.slice(0, 2).toLocaleLowerCase()"
        :color="selectedDays.includes(day) ? 'primary' : 'secondary'"
        @click="() => onDayClick(day)"
      />
    </q-btn-group>
  </div>
</template>

<script setup lang="ts">
import { allDays, allFrequencyTypes } from '@enums';
import { useScheduledRunConfiguration } from '@core/composables/useScheduledRunConfiguration';

// Use the composable to get all the scheduling logic
const {
  // Constants
  dateTimeMask,
  timeMask,
  inputMask,
  timeInputMask,

  // Reactive state
  scheduleName,
  selectedFrequency,
  selectedDays,
  currentInputValue,

  // Computed properties
  isStartDateVisible,
  isDaysVisible,
  requiresDateTime,
  isInvalid,

  // Functions
  onDayClick,
  getConfiguration,
} = useScheduledRunConfiguration();

defineExpose({ getConfiguration, isInvalid, scheduleName, selectedFrequency });
</script>

<style lang="scss" scoped>
.days {
  :deep(.q-btn) {
    padding: 4px 30px !important;
  }
}
</style>
